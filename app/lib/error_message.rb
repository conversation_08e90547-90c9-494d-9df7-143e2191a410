# frozen_string_literal: true

class ErrorMessage
  def self.not_found(record = 'record')
    "Sorry, #{record} not found."
  end

  def self.already_registered_email_credentials
    'Email ini telah terdaftar sebagai pengguna.'
  end

  def self.invalid_credentials
    'Invalid credentials'
  end

  def self.invalid_token
    'Invalid token'
  end

  def self.missing_token
    'Missing token'
  end

  def self.unauthorized
    'Unauthorized request'
  end

  def self.account_created
    'Account created successfully'
  end

  def self.account_not_created
    'Account could not be created'
  end

  def self.account_updated
    'Account updated successfully'
  end

  def self.expired_token
    'Sorry, your token has expired. Please login to continue.'
  end
end
