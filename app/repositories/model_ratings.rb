# frozen_string_literal: true

class ModelRatings < ::ApplicationRepository
  def default_scope
    ::ModelRating.all
  end

  def include_user
    @scope.includes(user: :memberships)
  end

  def filter_by_model_template_id(model_template_id)
    @scope.where(model_template_id: model_template_id)
  end

  def filter_by_user_id(user_id)
    @scope.where(user_id: user_id)
  end

  def filter_by_organization_id(organization_id)
    @scope.includes(:model_template).where(model_templates: { organization_id: organization_id })
  end

  def filter_by_comment(comment)
    comment = ActiveModel::Type::Boolean.new.cast(comment)

    if comment
      @scope.where.not(comment: nil)
    else
      @scope
    end
  end
end
