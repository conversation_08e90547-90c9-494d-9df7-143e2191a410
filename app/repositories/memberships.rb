# frozen_string_literal: true

class Memberships < ::ApplicationRepository
  sort_by :id, :desc

  def default_scope
    ::Membership.all
  end

  def filter_by_organization_id(organization_id)
    @scope.where(organization_id: organization_id)
  end

  def filter_by_organization_team_id(org_team_id)
    @scope.where('? = ANY(organization_team_ids)', org_team_id)
  end

  def filter_by_role(role)    
    @scope.where(role: role)
  end
end
