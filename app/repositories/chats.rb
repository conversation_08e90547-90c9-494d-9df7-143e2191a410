# frozen_string_literal: true

class Chats < ::ApplicationRepository
  sort_by :id, :desc

  def default_scope
    ::Chat.all
  end

  def filter_by_workspace_id(workspace_id)
    @scope.where(workspace_id: workspace_id)
  end

  def filter_by_chat_type(chat_type)
    @scope.where(chat_type: chat_type)
  end

  def filter_by_search(q)
    @scope.where('chats.name ILIKE ?', "%#{q}%")
  end

  def filter_by_ownership(user_id)
    join = 'LEFT JOIN workspaces_memberships wm ON ' + \
           'wm.workspace_id = chats.workspace_membership_workspace_id AND ' + \
           'wm.membership_id = chats.workspace_membership_membership_id ' + \
           'LEFT JOIN memberships m ON m.id = wm.membership_id'

    @scope.joins(join).where('m.user_id = ?', "#{user_id}")
  end

  def filter_by_test_prompt(test_prompt)
    test_prompt = ActiveModel::Type::Boolean.new.cast(test_prompt)

    if test_prompt
      @scope.joins('INNER JOIN model_templates ON model_templates.test_prompt_chat_id = chats.id')
    else
      where = 'model_templates.test_prompt_chat_id != chats.id OR ' \
              'model_templates.id IS NULL OR ' \
              '(chats.source_model_template_id IS NOT NULL AND model_templates.test_prompt_chat_id IS NULL)'
      @scope.joins('LEFT JOIN model_templates ON model_templates.id = chats.source_model_template_id')
            .where(where)
    end
  end
end
