# frozen_string_literal: true

module V1
  class ModelTemplateVariablesController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::ModelTemplateVariableCreationInput.new(request_body)
      validate! input, capture_failure: true

      template_variable = service.create(input.output)

      render_json template_variable, use: :format, status: :created
    end

    def update
      input = ::V1::ModelTemplateVariableUpdateInput.new(request_body)
      validate! input, capture_failure: true

      template_variable = service.update(params[:id], input.output)

      render_json template_variable, use: :format, status: :ok
    end

    def list
      result = service.list(query_params)

      render_json_array result.model_template_variables, use: :format, status: :ok
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::ModelTemplateVariableOutput
    end

    def service
      @service ||= ::ModelTemplateVariableService.new(current_user)
    end
  end
end
