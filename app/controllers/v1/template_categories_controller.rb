# frozen_string_literal: true

module V1
  class TemplateCategoriesController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::TemplateCategoryCreationInput.new(request_body)
      validate! input, capture_failure: true

      template_category = service.create(input.output)

      render_json template_category, use: :format, status: :created
    end

    def update
      input = ::V1::TemplateCategoryUpdateInput.new(request_body)
      validate! input, capture_failure: true

      template_category = service.update(params[:id], input.output)

      render_json template_category, use: :format, status: :ok
    end

    def show
      template_category = service.show(params[:id])

      render_json template_category, use: :format, status: :ok
    end

    def index
      result = service.index(query_params)

      render_json_array result.template_categories,
                        use: :format,
                        status: :ok,
                        used_template_count: result.used_template_count
    end

    def destroy
      input = ::V1::TemplateCategoryDeleteInput.new(request_body)
      validate! input, capture_failure: true

      service.destroy(params[:id], input.output)

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::TemplateCategoryOutput
    end

    def service
      @service ||= ::TemplateCategoryService.new(current_user)
    end
  end
end
