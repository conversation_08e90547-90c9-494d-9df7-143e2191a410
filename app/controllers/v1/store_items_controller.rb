# frozen_string_literal: true

module V1
  class StoreItemsController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::StoreItemCreationInput.new(request_body)
      validate! input, capture_failure: true

      store_item = service.create(input.output)

      render_json store_item, use: :format, status: :created
    end

    def update
      input = ::V1::StoreItemUpdateInput.new(request_body)
      validate! input, capture_failure: true

      store_item = service.update(params[:id], input.output)

      render_json store_item, use: :format, status: :ok
    end

    def show
      store_item = service.show(params[:id])

      render_json store_item, use: :format, status: :ok
    end

    def index
      store_item = service.index(query_params)

      render_json_array store_item, use: :format, status: :ok
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::StoreItemOutput
    end

    def service
      @service ||= ::StoreItemService.new(current_user)
    end
  end
end
