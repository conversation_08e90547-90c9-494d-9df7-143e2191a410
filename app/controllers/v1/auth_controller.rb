# frozen_string_literal: true

module V1
  class AuthController < ApiController
    authorize_auth_token! :all, only: [:show]

    def authenticate
      email = auth_params[:email].to_s.downcase.strip

      authenticate_user = AuthenticateUser.new(email, auth_params[:password])
      auth_token = authenticate_user.call
      user = authenticate_user.auth_user

      render_json auth_token,
                  ::V1::AuthOutput,
                  current_user: user
    end

    def show
      return render_error 'Not authorized', status: 401 unless current_user

      render_json auth_token, ::V1::AuthOutput, current_user:
    end

    private

    def auth_params
      params.permit(:email, :password)
    end
  end
end
