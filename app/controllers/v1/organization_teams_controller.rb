# frozen_string_literal: true

module V1
  class OrganizationTeamsController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::OrganizationTeamCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(input.output)

      render_json result.organization_team,
                  use: :format, status: :created
    end

    def update
      input = ::V1::OrganizationTeamUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.organization_team,
                  use: :format, status: :ok
    end

    def show
      result = service.show(params[:id])

      render_json result.organization_team,
                  use: :format
    end

    def index
      result = service.index(query_params)

      render_json_array result.organization_teams,
                        use: :format, status: :ok,
                        memberships: result.memberships
    end

    def destroy
      input = ::V1::OrganizationTeamDeleteInput.new(request_body)
      validate! input, capture_failure: true

      service.destroy(params[:id], input.output)

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::OrganizationTeamOutput
    end

    def service
      @service ||= ::OrganizationTeamService.new(current_user)
    end
  end
end
