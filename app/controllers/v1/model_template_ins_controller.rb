# frozen_string_literal: true

module V1
  class ModelTemplateInsController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::ModelTemplateInCreationInput.new(request_body)
      validate! input, capture_failure: true

      template_instruction_input = service.create(input.output)

      render_json template_instruction_input, use: :format, status: :created
    end

    def update
      input = ::V1::ModelTemplateInUpdateInput.new(request_body)
      validate! input, capture_failure: true

      template_instruction_input = service.update(params[:id], input.output)

      render_json template_instruction_input, use: :format, status: :ok
    end

    def index
      result = service.index(query_params)

      render_json_array result.template_instruction_inputs, use: :format, status: :ok
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::ModelTemplateInOutput
    end

    def service
      @service ||= ::ModelTemplateInService.new(current_user)
    end
  end
end
