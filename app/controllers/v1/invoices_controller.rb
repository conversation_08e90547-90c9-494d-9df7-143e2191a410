# frozen_string_literal: true

module V1
  class InvoicesController < ApiController
    authorize_auth_token! :all, except: [:handle_callback]

    def create
      input = ::V1::InvoiceCreationInput.new(request_body)
      validate! input, capture_failure: true

      invoice = service.create(input.output)

      render_json invoice, use: :format, status: :created
    end

    def show
      invoice = service.show(params[:id])

      render_json invoice, use: :format, status: :ok
    end

    def index
      invoices = service.index(query_params)

      render_json_array invoices, use: :format, status: :ok
    end

    def handle_callback
      input = ::V1::InvoiceHandleCallbackInput.new(request_body)
      validate! input, capture_failure: true

      service.handle_callback(input.output)

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::InvoiceOutput
    end

    def service
      @service ||= ::InvoiceService.new(current_user)
    end
  end
end
