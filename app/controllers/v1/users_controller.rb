# frozen_string_literal: true

module V1
  class UsersController < ApiController
    authorize_auth_token! :all

    def show
      id = user_id(params[:id])
      user = service.see(id)

      render_json user, use: :format
    end

    def get_by_email
      user = service.get_by_email(query_params[:email])

      render_json user, use: :format
    end

    def update
      input = ::V1::UserUpdateInput.new(request_body)
      validate! input, capture_failure: true

      output = input.output
      user = service.update(output)

      render_json user, use: :format
    end

    private

    def default_output
      ::V1::UserOutput
    end

    def service
      @service ||= ::UserService.new(current_user)
    end
  end
end
