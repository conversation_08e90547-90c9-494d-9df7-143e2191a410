# frozen_string_literal: true

module V1
  class ModelRatingsController < ApiController
    authorize_auth_token! :all

    def create
      input = ::V1::ModelRatingCreationInput.new(request_body)
      validate! input, capture_failure: true

      chat = service.create(input.output)

      render_json chat, ::V1::ModelRatingOutput, status: :created
    end

    def update
      input = ::V1::ModelRatingUpdateInput.new(request_body)
      validate! input, capture_failure: true

      chat = service.update(params[:id], input.output)

      render_json chat, ::V1::ModelRatingOutput, status: :ok
    end

    def index
      result = service.index(query_params)

      render_json_array result.model_ratings,
                        ::V1::ModelRatingOutput,
                        use: :format,
                        users: result.users
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def service
      @service ||= ::ModelRatingService.new(current_user)
    end
  end
end
