# frozen_string_literal: true

module V1
  class ModelTemplatesController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      render_json_array result.templates,
                        ::V1::ModelTemplateOutput,
                        use: :format,
                        current_user: current_user,
                        variables: result.variables,
                        template_categories: result.template_categories,
                        number_of_used_times: result.number_of_used_times,
                        organization_teams: result.organization_teams,
                        ratings: result.ratings,
                        users_with_assigned_team: result.users_with_assigned_team,
                        show_editable: true
    end

    def show
      result = service.show(params[:id])

      render_json result.template,
                  ::V1::ModelTemplateOutput,
                  use: :format,
                  current_user: current_user,
                  variables: result.variables,
                  instruction_inputs: result.instruction_inputs,
                  template_categories: result.template_categories,
                  organization_teams: result.organization_teams,
                  ratings: result.ratings
    end

    def create
      input = ::V1::ModelTemplateCreationInput.new(request_body)
      validate! input, capture_failure: true

      binding.pry

      result = service.create(input.output)

      render_json result.template,
                  ::V1::ModelTemplateOutput,
                  use: :format, status: :created,
                  current_user: current_user,
                  variables: result.variables,
                  template_categories: result.template_categories
    end

    def update
      input = ::V1::ModelTemplateUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.template,
                  ::V1::ModelTemplateOutput,
                  use: :format, status: :ok,
                  current_user: current_user,
                  variables: result.variables,
                  template_categories: result.template_categories
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    def list_comments
      result = service.list_comments(params[:model_template_id])

      render_json_array result.comments,
                        ::V1::ModelRatingOutput,
                        number_of_used_times: result.number_of_used_times,
                        users: result.users
    end

    def duplicate
      result = service.duplicate(params[:model_template_id])

      render_json result.template,
                  ::V1::ModelTemplateOutput,
                  use: :format, status: :ok,
                  current_user: current_user,
                  variables: result.variables,
                  template_categories: result.template_categories,
                  instruction_inputs: result.instruction_inputs
    end

    private

    def default_output
      ::V1::ModelTemplateOutput
    end

    def service
      @service ||= ::ModelTemplateService.new(current_user)
    end
  end
end
