# frozen_string_literal: true

class AuthTokenMiddleware < ApplicationMiddleware
  def initialize(app, permissions, required: true)
    super(app)
    @permissions = Array(permissions)
    @required = required
  end

  def call(env)
    request = ActionDispatch::Request.new(env)

    user = capture_error do
      (AuthorizeApiRequest.new(request.headers).call)[:user]
    end

    env['collabway.auth_token'] = bearer_auth(request)

    return error(*@error) if @error && !user && (@permissions.exclude? :public)

    Current.user = user || User.new
    # Honeybadger.context(user)
    # Sentry.set_user(id: user.id, email: user.email) if user

    super
  end

  private

  def bearer_auth(request)
    request.authorization.to_s.match(/\A\W*bearer\W+(.*)/i).to_a.second
  end

  def capture_error
    yield
  rescue ExceptionHandler::Unauthorized => e
    @error ||= [403, e.message]
    nil
  rescue StandardError => _e
    @error ||= [401, 'Request not authenticated']
    nil
  end
end
