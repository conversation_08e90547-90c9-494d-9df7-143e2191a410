# frozen_string_literal: true

class ApplicationMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    @app.call(env)
  end

  private

  def error(status, error)
    output = if error.is_a?(Exception)
               ::ExceptionOutput
             else
               ::ErrorOutput
             end

    json = output.new(error, status: status).to_json

    charset = ActionDispatch::Response.default_charset
    headers = {
      'Content-Type' => "application/json; charset=#{charset}",
      'Content-Length' => json.bytesize.to_s
    }

    [status, headers, [json]]
  end
end
