# frozen_string_literal: true

class User < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  has_secure_password

  before_create do |user|
    next if Rails.env.development? || Rails.env.test?
    user.id = SecureRandom.uuid
  end

  has_many :memberships

  validates_presence_of :display_name, :email, :password_digest

  def authenticate(unencrypted_password)
    BCrypt::Password.new(password_digest).is_password?(unencrypted_password) && self
  end

  def membership
    memberships.first
  end
end
