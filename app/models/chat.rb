class Chat < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  has_many :messages

  has_one :openai_chat

  belongs_to :model
  belongs_to :workspace

  enum chat_type: string_enum('general', 'prompt_builder')

  after_initialize :set_external_id

  def self.find_by_user(chat_id, user_id)
    find_by!(id: chat_id, user_id: user_id)
  end

  def set_external_id
    return if external_id

    self.external_id = SecureRandom.uuid
    self.save
  end
end
