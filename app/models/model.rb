class Model < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization, optional: true
  belongs_to :model_template, optional: true

  has_many :chats

  enum model: string_enum('gpt-4o', 'gpt-4o-mini', 'openai/gpt-4o', 'openai/gpt-4o-mini',
                          'deepseek/deepseek-r1', 'google/gemini-2.5-pro-preview', 'deepseek/deepseek-chat-v3-0324:free')

  # validate :valid_model_template_id

  # def valid_model_template_id
  #   valid = self.model_template_id.nil? || Chat.exists?(id: self.model_template_id)

  #   self.errors.add :base, 'Model template does not exist' if !valid
  # end
end
