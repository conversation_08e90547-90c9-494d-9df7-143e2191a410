class ModelTemplate < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :test_prompt_chat, optional: true, class_name: 'Chat', foreign_key: 'test_prompt_chat_id'
  belongs_to :template_category, optional: true
  belongs_to :workspaces_membership, optional: true
  belongs_to :user
  belongs_to :organization_team, optional: true

  enum template_type: string_enum('default', 'expert')

  has_many :model_ratings
  has_many :model_template_variables
  has_many :model_template_ins

  # validate :valid_test_prompt_chat_id

  # def valid_test_prompt_chat_id
  #   valid = self.test_prompt_chat_id.nil? || Chat.exists?(id: self.test_prompt_chat_id)

  #   self.errors.add :base, 'Test prompt chat does not exist' if !valid
  # end
end
