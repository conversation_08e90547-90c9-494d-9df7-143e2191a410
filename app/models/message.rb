class Message < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :chat
  has_many :web_search_results, dependent: :destroy
  enum status_on_thread: string_enum('present', 'removed', 'hidden')
  enum model_used: string_enum('gpt-4o', 'gpt-4o-mini', 'openai/gpt-4o', 'openai/gpt-4o-mini',
                               'deepseek/deepseek-r1', 'google/gemini-2.5-pro-preview', 'deepseek/deepseek-chat-v3-0324:free')
end
