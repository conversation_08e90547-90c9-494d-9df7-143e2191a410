# frozen_string_literal: true

class UserInvitation < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization
  belongs_to :invited_by_membership,  
             class_name: 'Membership',
             foreign_key: 'invited_by_membership_id'

  belongs_to :invited_to_organization_team,
             optional: true,
             class_name: 'OrganizationTeam',
             foreign_key: 'invited_to_organization_team_id'
end
