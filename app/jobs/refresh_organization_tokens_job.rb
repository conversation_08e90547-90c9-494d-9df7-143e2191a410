class RefreshOrganizationTokensJob < ApplicationJob
  queue_as :default

  def perform
    current_time = Time.current
    current_day = current_time.day
    end_of_month_day = current_time.end_of_month.day

    ActiveRecord::Base.transaction do
      org_plans = []
      if current_day == end_of_month_day
        org_plans = OrganizationsPlansThreshold.where(refresh_date: current_day..)
      else
        org_plans = OrganizationsPlansThreshold.where(refresh_date: current_day)
      end

      org_plans.each do |org_plan|
        org_plan.update(remaining_monthly_credits: org_plan.monthly_credits_refresh)

        CreditHistory.create!(
          organization_id: org_plan.organization_id,
          action: 'su_monthly_refresh',
          monthly_credits: org_plan.monthly_credits_refresh,
          purchased_credits: 0,
          action_at: current_time
        )
      end
    end
  end
end
