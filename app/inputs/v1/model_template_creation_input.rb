# frozen_string_literal: true

module V1
  class ModelTemplateCreationInput < ::ApplicationInput
    required(:name)
    optional(:description)
    optional(:max_tokens)
    optional(:model)
    optional(:instruction)
    optional(:prompt)
    optional(:placeholder)
    optional(:template_type)
    optional(:verified)
    optional(:category)
    optional(:organization_prompt)
    optional(:draft)
    optional(:reference_output_url)
    optional(:template_category_id)
    optional(:organization_team_id)

    # validates :reference_output_url, format: { with: URI::regexp(%w(http https)), message: "Valid URL required"}
  end
end
