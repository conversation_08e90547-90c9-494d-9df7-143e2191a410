# frozen_string_literal: true

module V1
  class ChatCreationInput < ::ApplicationInput
    required(:name)
    optional(:placeholder)
    optional(:source_model_template_id)
    required(:workspace_id)
    required(:model).any_of(['gpt-4o', 'gpt-4o-mini', 'openai/gpt-4o', 'openai/gpt-4o-mini',
                             'deepseek/deepseek-r1', 'google/gemini-2.5-pro-preview', 'deepseek/deepseek-chat-v3-0324:free', 'deepseek/deepseek-chat-v3-0324:free'])
    required(:chat_type).any_of(%w[general prompt_builder])
    optional(:verified)
    optional(:credits)
    optional(:category)
  end
end
