# frozen_string_literal: true

class AuthorizeApiRequest
  def initialize(headers = {})
    @headers = headers
  end

  def call
    { user: }
  end

  private

  attr_reader :headers

  def user
    method = decoded_auth[:method]
    arg = decoded_auth[:arg]
    value = decoded_auth[:value]

    @user ||= User.send(method, arg, value) if decoded_auth
  rescue ActiveRecord::RecordNotFound => e
    raise(
      ExceptionHandler::InvalidToken,
      ("#{ErrorMessage.invalid_token} #{e.message}")
    )
  end

  def decoded_auth_token
    decoded = JsonWebToken.decode(http_auth_header)

    @decoded_auth_token ||= decoded.merge(
      method: :find_by!,
      arg: 'id = ?',
      value: decoded[:user_id] || decoded[:sub]
    )
  end

  def decoded_auth
    decoded_auth_token
  end

  def http_auth_header
    return headers['Authorization'].split(' ').last if headers['Authorization'].present?

    raise(ExceptionHandler::MissingToken, ErrorMessage.missing_token)
  end

  def auth_mechanism
    headers['Authorization'].to_s.split(' ').first&.downcase
  end
end
