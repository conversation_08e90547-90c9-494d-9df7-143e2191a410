# frozen_string_literal: true

class AuthenticateUser
  attr_reader :auth_user

  def initialize(email, password, auth_user = nil, _referer = nil)
    @email = email
    @password = password
    @auth_user = auth_user
  end

  def call
    return unless user

    payload = { user_id: user.id }

    # because weird error in webapp
    # create token that practically last forever
    @auth_user = user
    JsonWebToken.encode(payload, exp=10.years)
  end

  private

  attr_reader :email, :password

  def user
    user = User.find_by(email:)

    raise ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials unless user
    return user if user.authenticate(password)

    raise(ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials)
  end
end
