class UserInvitationMailer < ApplicationMailer
  default from: <PERSON>NV['MAILER_EMAIL_ADDRESS']

  def send_email
    @user_invitation = params[:user_invitation]
    @user_name = @user_invitation.email.split('@').first
    @invited_by_user = @user_invitation.invited_by_membership.user
    @organization = @user_invitation.organization
    @base_app = ENV['WEBAPP']
    @cs_email = ENV['CUSTOMER_SERVICE_EMAIL_ADDRESS']
    @accept_invite_link = "#{@base_app}/accept-invitation?invite_code=#{@user_invitation.invitation_code}"
    
    mail(to: @user_invitation.email, subject: "You've Been Invited to Join a TuneAI Team")
  end
end
