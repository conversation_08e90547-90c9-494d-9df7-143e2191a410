class OnboardOrganizationMailer < ApplicationMailer
    default from: ENV['MAILER_EMAIL_ADDRESS']

  def send_email
    @membership = params[:membership]
    @user = @membership.user
    @user_name = @user.display_name.split(' ').first
    @organization = @membership.organization
    @base_app = ENV['WEBAPP']
    @cs_email = ENV['CUSTOMER_SERVICE_EMAIL_ADDRESS']

    subject = "Welcome to TuneAI! Your Account Is Ready"

    mail(to: @user.email, subject: subject)
  end
end
