# frozen_string_literal: true

class ModelRatingService < ::AppService
  def initialize(user)
    @user = user
  end

  def create(params)
    params = params.merge(user_id: @user.id)
    model_rating = ModelRating.create!(params)

    model_rating
  end

  def update(id, params)
    params.delete(:user_id)
    params.delete(:model_template_id)

    model_rating = ModelRating.find(id)
    authorize! rating_ownership(model_rating)

    model_rating.update(params)
    model_rating
  end

  def index(query_params)
    model_ratings = ::ModelRatings.new

    filter = query_params.slice(:model_template_id, :user_id, :comment, :disable_pagination)

    filter = filter.merge(
      organization_id: @user.membership.organization_id
    )

    ratings = model_ratings.filter(filter)

    user_ids = ratings.pluck(:user_id)
    users = User.includes(:memberships).where(id: user_ids)

    OpenStruct.new(
      model_ratings: ratings,
      users: users
    )
  end

  def destroy(id)
    model_rating = ModelRating.find(id)
    authorize! rating_ownership(model_rating)

    model_rating.discard!
  end

  private

  def rating_ownership(rating)
    @user.id == rating.user_id
  end
end
