# frozen_string_literal: true

class MembershipService < ::AppService
  def initialize(user)
    @user = user
  end

  def index(query_params)
    membership = verify_user_organization(@user)

    memberships = ::Memberships.new

    filter = query_params.slice(
      :role, :organization_team_id, :name, :page, :per_page
    )

    if filter[:organization_team_id].present?
      org_team = OrganizationTeam.find_by(id: filter[:organization_team_id], organization_id: membership.organization_id)
      assert! org_team.present?, on_error: 'Team Invalid'
    end

    if filter[:role].present?
      if filter[:role].is_a?(Array)
        roles_str = filter[:role]
      else
        begin
          roles_str = JSON.parse(filter[:role])
        rescue JSON::ParserError
          raise Invalid, 'Role(s) Format Invalid'
        end
      end
      
      roles_int = roles_str.map { |r| Membership.role_mappings[r.downcase] }.compact
      filter[:role] = roles_int
    end

    filter[:organization_id] = membership.organization_id

    filtered = memberships.filter(filter)

    OpenStruct.new(
      memberships: filtered,
      users: User.where(id: filtered.pluck(:user_id).compact),
      organization_teams: OrganizationTeam.where(id: filtered.pluck(:organization_team_ids).union.flatten.uniq.compact)
    )
  end

  def update(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin', 'team_admin'])

    to_be_updated_membership = Membership.find(id)
    authorize! to_be_updated_membership.organization_id == membership.organization_id,
               on_error: 'User Invalid'
    authorize! to_be_updated_membership.role > membership.role,
               on_error: 'Cannot update with role higher access than your role'

    organization_team_ids = params.delete(:organization_team_ids)
    assert! !organization_team_ids.nil?, on_error: 'Input Invalid'

    new_role = params.delete(:role)
    new_display_name = params.delete(:name)

    if new_role.present?
      role_number = Membership.role_mappings[new_role]
      assert! role_number.present?, on_error: 'Role not found'
    end

    if !organization_team_ids.is_a?(Array)
      begin
        organization_team_ids = JSON.parse(organization_team_ids)
      rescue JSON::ParserError
        raise Invalid, 'Organization Team IDs Format Invalid'
      end
    end

    assert! !organization_team_ids.nil? && organization_team_ids.is_a?(Array), on_error: 'Input Invalid'
    
    input_org_team = OrganizationTeam.where(id: organization_team_ids, organization_id: membership.organization_id).count
    assert! input_org_team == organization_team_ids.size, on_error: 'Team Invalid'

    ActiveRecord::Base.transaction do
      organization_team_ids = organization_team_ids.compact.uniq.sort
      to_be_updated_membership.organization_team_ids = organization_team_ids

      if new_role.present?
        to_be_updated_membership.role = role_number
      end

      if new_display_name.present?
        to_be_updated_user = to_be_updated_membership.user
        to_be_updated_user.update(display_name: new_display_name)
      end

      to_be_updated_membership.save!
    end

    to_be_updated_membership
  end

  def destroy(id)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin', 'team_admin'])

    to_be_deleted_membership = Membership.find(id)
    authorize! to_be_deleted_membership.organization_id == membership.organization_id,
               on_error: 'User Invalid'
    authorize! to_be_deleted_membership.role > membership.role,
               on_error: 'Cannot delete with role higher access than your role'

    ActiveRecord::Base.transaction do
      to_be_deleted_membership.discard!
    end
  end

  def organization_id
    @user.membership.organization_id
  end
end
