# frozen_string_literal: true

class OrganizationTeamService < ::AppService
  def initialize(user)
    @user = user
  end

  def create(params)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin'])

    organization_team = OrganizationTeam.new

    organization_id = @user.membership.organization_id
    params[:organization_id] = organization_id

    ActiveRecord::Base.transaction do
      organization_team = OrganizationTeam.create!(params)
    end

    OpenStruct.new(
      organization_team: organization_team
    )
  end

  def update(id, params)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin'])

    organization_team = OrganizationTeam.find(id)
    organization = Organization.find_by(id: organization_team.organization_id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    ActiveRecord::Base.transaction do
      organization_team.update(params)
    end

    OpenStruct.new(
      organization_team: organization_team
    )
  end

  def show(id)
    organization_team = OrganizationTeam.find(id)
    organization = Organization.find_by(id: organization_team.organization_id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    OpenStruct.new(
      organization_team: organization_team
    )
  end 

  def index(query_params)
    organization_teams = ::OrganizationTeams.new

    filter = query_params.slice(:organization_id, :search, :page, :per_page)

    unless @user.membership.membership_role == 'super_user'
      filter[:organization_id] = @user.membership.organization_id
    end

    filtered = organization_teams.filter(filter)
    org_team_ids = filtered.pluck(:id)

    included_memberships = Membership.where("memberships.organization_team_ids && array#{org_team_ids}")

    OpenStruct.new(
      organization_teams: filtered,
      memberships: included_memberships
    )
  end

  def destroy(id, params)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin'])

    organization_team = OrganizationTeam.find(id)
    organization = Organization.find_by(id: organization_team.organization_id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    new_organization_team_id = params.delete(:new_organization_team_id)

    ActiveRecord::Base.transaction do
      if new_organization_team_id.present?
        new_organization_team = OrganizationTeam.find(new_organization_team_id)
        new_organization = Organization.find_by(id: new_organization_team.organization_id)
        authorize! organization_ownership(new_organization), on_error: 'Not owned organization!'

        # change model template organization team id
        ModelTemplate.where(organization_team_id: id)
                     .update_all(organization_team_id: new_organization_team_id)
        
        # change membership organization team id
        memberships = Membership.where('? = ANY(organization_team_ids)', id)
        memberships.each do |m|
          new_org_team_ids = m.organization_team_ids - [id.to_i] + [new_organization_team_id.to_i]
          new_org_team_ids = new_org_team_ids.compact.uniq.sort
          m.update(organization_team_ids: new_org_team_ids)
        end
      end

      organization_team.discard!
    end
  end

  private

  def organization_ownership(organization)
    return true if @user.membership.membership_role == 'super_user'
  
    organization.id == @user.membership.organization_id
  end
end
