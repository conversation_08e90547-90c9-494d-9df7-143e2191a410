# frozen_string_literal: true

class TemplateCategoryService < ::AppService
  def initialize(user)
    @user = user
  end

  def create(params)
    verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin', 'team_admin'])

    params[:organization_id] = @user.membership&.organization_id
    params[:validated] = true
    params[:general_category] = false

    ActiveRecord::Base.transaction do
      TemplateCategory.create!(params)
    end
  end

  def update(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin', 'team_admin'])

    params = params.slice(:name)

    category = TemplateCategory.find(id)
    authorize! category_ownership(membership, category)

    ActiveRecord::Base.transaction do
      category.update(params)
    end

    category
  end

  def show(id)
    membership = verify_user_organization(@user)

    category = TemplateCategory.find(id)
    authorize! category_ownership(membership, category)

    category
  end

  def index(query)
    membership = verify_user_organization(@user)
    template_categories = ::TemplateCategories.new

    filter = query.slice(:search, :page, :per_page)

    unless membership.membership_role == 'super_user'
      filter = filter.merge(
        organization_id: membership.organization_id
      )
    end

    filtered = template_categories.filter(filter)
    used_template_count = ModelTemplate.where(
                                         organization_id: membership.organization_id,
                                         template_category_id: filtered.pluck(:id).compact.uniq
                                       )
                                       .group(:template_category_id)
                                       .count

    OpenStruct.new(
      template_categories: filtered,
      used_template_count: used_template_count
    )
  end

  def destroy(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin', 'team_admin'])

    category = TemplateCategory.find(id)
    authorize! category_ownership(membership, category)

    new_template_category_id = params.delete(:new_template_category_id)

    ActiveRecord::Base.transaction do
      if new_template_category_id.present?
        new_category = TemplateCategory.find(id)
        authorize! category_ownership(membership, new_category)

        # change model template template category id
        ModelTemplate.where(template_category_id: id)
                     .update_all(template_category_id: new_template_category_id)
      end

      category.discard!
    end
  end

  private

  def category_ownership(membership, category)
    membership.organization_id == category&.organization_id
  end
end
