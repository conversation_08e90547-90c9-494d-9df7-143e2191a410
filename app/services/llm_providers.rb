# frozen_string_literal: true

class LLMProviders
  class << self
    def provider_for_model(model_name)
      case model_name
      when /^gpt-4o/, /^gpt-4o-mini/
        :openai
      when %r{^openai/gpt-4o}, %r{^openai/gpt-4o-mini}
        :openrouter
      when /^deepseek/, /^google/
        :openrouter
      else
        raise "Unknown model: #{model_name}"
      end
    end

    def get_service(user, model_name)
      case provider_for_model(model_name)

      when :openai
        OpenaiService.new(user)
      when :openrouter
        OpenrouterService.new(user)
      end
    end
  end
end
