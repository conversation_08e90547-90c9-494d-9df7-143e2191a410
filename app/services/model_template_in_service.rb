# frozen_string_literal: true

class ModelTemplateInService < ::AppService
  def initialize(user)
    @user = user
    @openai_service = OpenaiService.new(@user)
  end

  def create(params)
    template = ModelTemplate.find_by(id: params[:model_template_id])
    authorize! template_ownership(template)

    model = Model.where(model_template_id: params[:model_template_id]).where.not(openai_assistant_id: nil).first
    exist! model.present?

    template_input = ModelTemplateIn.new

    ActiveRecord::Base.transaction do
      template_input = ModelTemplateIn.create!(params)

      # # Upload file to OpenAI if file present
      # file_url = template_input.input_reference_url
      # if file_url.present?
      #   assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)
      #   vector_store_id = assistant_response['tool_resources']['file_search']['vector_store_ids'].first
      #   if vector_store_id.nil?
      #     vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
      #     vector_store_id = vs_response['id']

      #     tool_resources = {
      #       file_search: {
      #         vector_store_ids: [vs_response['id']]
      #       }
      #     }

      #     @openai_service.modify_assistant(model, tool_resources: tool_resources)
      #   end

      #   response_file = @openai_service.create_file(file_url, 'assistants')
        
      #   OpenaiFile.create!(
      #     object_id: template_input.id,
      #     object_class: template_input.class.name,
      #     object_class_column: 'input_reference_url',
      #     openai_file_id: response_file['id']
      #   )

      #   @openai_service.create_assistant_files(
      #     vector_store_id,
      #     mode: 'openai_file_ids',
      #     openai_file_ids: [response_file['id']]
      #   )
      # end
    end

    template_input
  end

  def update(id, params)
    template_input = ModelTemplateIn.find(id)

    template = template_input.model_template
    authorize! template_ownership(template)

    model = Model.find_by!(model_template_id: template_input.model_template_id)

    ActiveRecord::Base.transaction do
      # current_file_url = template_input.input_reference_url

      template_input.update!(params)
      # latest_file_url = template_input.input_reference_url

      # # Update OpenAI file if variable reference is changed
      # if current_file_url != latest_file_url
      #   openai_file = OpenaiFile.find_by(
      #     object_id: template_input.id,
      #     object_class: template_input.class.name,
      #     object_class_column: 'input_reference_url'
      #   )
      #   if openai_file.present?
      #     begin
      #       response_file = @openai_service.delete_file(openai_file.openai_file_id)
      #       if response_file['deleted']
      #         openai_file.discard!
      #       end
      #     rescue Faraday::ResourceNotFound
      #       response_file = {}
      #       openai_file.discard!
      #     end
      #   end

      #   # TODO error handling
      #   if latest_file_url.present?
      #     assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)
      #     vector_store_id = assistant_response['tool_resources']['file_search']['vector_store_ids'].first
      #     if vector_store_id.nil?
      #       vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
      #       vector_store_id = vs_response['id']

      #       tool_resources = {
      #         file_search: {
      #           vector_store_ids: [vs_response['id']]
      #         }
      #       }

      #       @openai_service.modify_assistant(model, tool_resources: tool_resources)
      #     end

      #     response_file = @openai_service.create_file(latest_file_url, 'assistants')

      #     OpenaiFile.create!(
      #       object_id: template_input.id,
      #       object_class: template_input.class.name,
      #       object_class_column: 'input_reference_url',
      #       openai_file_id: response_file['id']
      #     )

      #     @openai_service.create_assistant_files(
      #       vector_store_id,
      #       mode: 'openai_file_ids',
      #       openai_file_ids: [response_file['id']]
      #     )
      #   end
      # end
    end

    template_input
  end

  def index(query_params)
    model_template_ins = ::ModelTemplateIns.new

    filter = query_params.slice(
      :model_template_id,
      :search
    )
    filter = filter.merge(
      organization_id: @user.membership&.organization_id
    )

    filtered = model_template_ins.filter(filter)

    OpenStruct.new(
      template_instruction_inputs: filtered
    )
  end

  def destroy(id)
    template_input = ModelTemplateIn.find(id)

    template = template_input.model_template
    authorize! template_ownership(template)

    ActiveRecord::Base.transaction do
      # openai_file = OpenaiFile.find_by(
      #   object_id: template_input.id,
      #   object_class: template_input.class.name,
      #   object_class_column: 'input_reference_url'
      # )
      # if openai_file.present?
      #   begin
      #     response_file = @openai_service.delete_file(openai_file.openai_file_id)
      #   rescue Faraday::ResourceNotFound
      #     response_file = {}
      #   end
  
      #   openai_file.discard!
      # end
      template_input.discard!
    end
  end

  private

  def template_ownership(template)
    @user.membership&.organization_id == template.organization_id
  end
end
