# frozen_string_literal: true

class StoreItemService < ::AppService
  def initialize(user)
    @user = user
  end

  def create(params)
    verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user'])

    ActiveRecord::Base.transaction do
      StoreItem.create!(params)
    end
  end

  def update(id, params)
    verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user'])

    params = params.slice(
      :name, :store_item_type, :token_amounts,
      :price, :price_decimal, :currency
    )

    store_item = StoreItem.find(id)

    ActiveRecord::Base.transaction do
      store_item.update(params)
    end

    store_item
  end

  def show(id)
    verify_user_organization(@user)

    store_item = StoreItem.find(id)

    store_item
  end

  def index(query)
    verify_user_organization(@user)
    store_items = ::StoreItems.new

    filter = query.slice(:search, :page, :per_page)

    store_items.filter(filter)
  end

  def destroy(id)
    verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user'])

    store_item = StoreItem.find(id)

    ActiveRecord::Base.transaction do
      store_item.discard!
    end
  end
end
