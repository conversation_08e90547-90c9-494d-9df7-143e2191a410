# frozen_string_literal: true

class CitationService < ::AppService
  def initialize(user)
    @user = user
  end

  def show(id)
    web_search_result = WebSearchResult.find(id)
    authorize! chat_ownership(web_search_result.message.chat)

    format_web_search_result(web_search_result)
  rescue ActiveRecord::RecordNotFound
    raise NotFoundError, 'Citation not found'
  end

  private

  def chat_ownership(chat)
    chat.workspace_membership_membership_id == @user.membership.id
  end

  def format_web_search_result(web_search_result)
    {
      message_id: web_search_result.message_id,
      id: web_search_result.id,
      url: web_search_result.url,
      title: web_search_result.title,
      content: web_search_result.content,
      start_index: web_search_result.start_index,
      end_index: web_search_result.end_index,
      created_at: web_search_result.created_at
    }
  end
end
