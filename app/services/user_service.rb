# frozen_string_literal: true

class UserService < ::AppService
  def initialize(user)
    @user = user
  end

  def see(id)
    authorize! can_see? id

    User.find(id)
  end

  def get_by_email(email)
    authorize_user_roles!(@user, ['super_user'])

    user = User.find_by(email: email)
    exist! user

    user
  end

  def update(params)
    if params[:password]
      assert! @user.authenticate(params[:current_password]), on_error: 'Invalid current password. Try again'
    end

    @user.update!(params.except(:current_password))
    @user
  end

  private

  def can_see?(id)
    @user.id.to_s == id.to_s
  end
end
