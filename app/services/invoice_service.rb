# frozen_string_literal: true

class InvoiceService < ::AppService
  def initialize(user)
    @user = user
  end

  def create(params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, ['super_user', 'owner', 'super_admin'])

    current_time = Time.current
    store_item = StoreItem.find(params[:store_item_id])

    request_id = SecureRandom.uuid
    existing_req_id = Invoice.find_by(payment_request_id: request_id)&.id
    authorize! existing_req_id.blank?, on_error: 'Request Failed. Please try again!'

    merchant_order_id = "Merchant_Order_" + SecureRandom.uuid
    existing_merchant_id = Invoice.find_by(payment_merchant_order_id: merchant_order_id)&.id
    authorize! existing_merchant_id.blank?, on_error: 'Request Failed. Please try again!'

    params = {
      name: "Invoice-#{request_id}",
      description: "Invoice #{store_item.name} for Organization: #{membership.organization&.name}",
      status: 'pending',
      user_id: membership.user_id,
      organization_id: membership.organization_id,
      payment_request_id: request_id,
      payment_merchant_order_id: merchant_order_id,
      checkout_price: store_item.price,
      checkout_price_decimal: store_item.price_decimal,
      checkout_token_amounts: store_item.token_amounts,
      checkout_currency: store_item.currency,
      store_item_id: store_item.id,
      expired_at: current_time + 10.minutes
    }
    invoice = Invoice.new

    ActiveRecord::Base.transaction do
      invoice = Invoice.create!(params)

      # GET JWT token
      expire_threshold = Time.current + 1.minutes
      airwallex_token = AirwallexToken.find_by(expires_at: expire_threshold..)

      if airwallex_token.blank?
        data = login_airwallex()
        airwallex_token = AirwallexToken.create(
          expires_at: data['expires_at'],
          token: data['token']
        )
      end

      # Create Airwallex Payment Intent
      url = "#{ENV['AIRWALLEX_HOSTNAME']}/api/v1/pa/payment_intents/create"
      headers = {
        'Authorization' => "Bearer #{airwallex_token.token}",
        'Content-Type' => "application/json"
      }
      payload = {
        request_id: request_id,
        amount: "#{params[:checkout_price]}.#{params[:checkout_price_decimal]}",
        currency: params[:checkout_currency],
        merchant_order_id: merchant_order_id
      }
      response = RestClient.post(url, payload.to_json, headers)
      data = JSON.parse(response.body)
  
      invoice.update(
        payment_intent_id: data['id'],
        payment_client_secret: data['client_secret'],
        payment_status: data['status']
      )
    end

    invoice.reload
    invoice
  end

  def show(id)
    membership = verify_user_organization(@user)

    invoice = Invoice.find_by(id: id, organization_id: membership.organization_id)
    exist! invoice.present?

    invoice
  end

  def index(query)
    membership = verify_user_organization(@user)
    invoices = ::Invoices.new

    filter = query.slice(:search, :page, :per_page)

    filter = filter.merge(
      organization_id: membership.organization_id
    )

    invoices.filter(filter)
  end

  def handle_callback(params)
    payment_intent_id = params[:payment_intent_id]

    invoice = Invoice.find_by(payment_intent_id: payment_intent_id)
    exist! invoice.present?

    # GET JWT token
    expire_threshold = Time.current + 1.minutes
    airwallex_token = AirwallexToken.find_by(expires_at: expire_threshold..)

    if airwallex_token.blank?
      data = login_airwallex()
      airwallex_token = AirwallexToken.create(
        expires_at: data['expires_at'],
        token: data['token']
      )
    end

    # Retrieve Airwallex Payment Intent
    url = "#{ENV['AIRWALLEX_HOSTNAME']}/api/v1/pa/payment_intents/#{payment_intent_id}"
    headers = {
      'Authorization' => "Bearer #{airwallex_token.token}"
    }
    response = RestClient.get(url, headers)
    data = JSON.parse(response.body)
    resp_intent_status = data['status']

    return if ['SUCCEEDED', 'CANCELLED'].include?(invoice.payment_status)

    
    ActiveRecord::Base.transaction do
      invoice.lock!
      current_time = Time.current

      # success & not processed
      if invoice.status == 'pending' && resp_intent_status == 'SUCCEEDED'
        invoice.update(status: 'paid', payment_status: 'SUCCEEDED')

        plans = OrganizationsPlansThreshold.find_by(organization_id: invoice.organization_id)
        plans.update(purchased_credits: plans.purchased_credits + invoice.checkout_token_amounts)

        CreditHistory.create!(
          organization_id: invoice.organization_id,
          action: 'org_topup',
          monthly_credits: 0,
          purchased_credits: invoice.checkout_token_amounts,
          invoice_id: invoice.id,
          action_at: current_time,
          user_id: invoice.user_id
        )

      # cancelled & not processed
      elsif resp_intent_status == 'CANCELLED'
        invoice.update(status: 'cancelled', payment_status: 'CANCELLED')
      end
    end
  end

  private

  # Login Airwallex
  def login_airwallex
    url = "#{ENV['AIRWALLEX_HOSTNAME']}/api/v1/authentication/login"
    headers = {
      'x-client-id' => ENV['AIRWALLEX_CLIENT_ID'],
      'x-api-key' => ENV['AIRWALLEX_API_KEY'],
    }
    response = RestClient.post(url, {}, headers)
    data = JSON.parse(response.body)

    return data
  end
end
