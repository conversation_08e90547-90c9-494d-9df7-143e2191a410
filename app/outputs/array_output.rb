# frozen_string_literal: true

class ArrayOutput < ApiOutput
  def root_json
    json = {
      root_key => format,
      :pagination => pagination_format
    }

    json.merge!(@options[:metadata]) unless @options[:metadata].nil?
    json.as_json
  end

  def scope
    @scope ||= @object.scope
  rescue StandardError
    nil
  end

  def format
    Array(@object).map { |o| item_output.new(o, item_options) }
  end

  def pagination_format
    return {} unless scope && !disable_pagination

    {
      current_page: scope.current_page,
      next_page: scope.next_page,
      prev_page: scope.prev_page,
      total_pages: scope.total_pages,
      total_count: scope.total_count,
      size: scope.size
    }
  end

  def root_key
    @options.fetch(:root) { item_output.root_key }
  end

  def item_options
    @options.except(:item_output, :root, :status)
  end

  def item_output
    @options[:item_output]
  end

  def disable_pagination
    @object.options[:disable_pagination]
  end
end
