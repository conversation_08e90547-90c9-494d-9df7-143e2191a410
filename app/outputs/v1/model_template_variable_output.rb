# frozen_string_literal: true

module V1
  class ModelTemplateVariableOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description,
        weight: @object.weight,
        references: @object.references,
        order: @object.order,
        model_template_id: @object.model_template_id,
        variable_reference: url_output(@object.variable_reference_url)
      }
    end

    def url_output(url)
      return unless url

      uri = URI.parse(url)
      filename = File.basename(uri.path)
      {
        filename: filename,
        url: url
      }
    end
  end
end
