# frozen_string_literal: true

module V1
  class TemplateCategoryOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        organization_id: @object.organization_id,
        used_template_count: used_template_count_output
      }
    end

    def used_template_count_output
      return if used_template_count.blank?

      curr_template_count = used_template_count[@object.id]

      return if curr_template_count.blank?

      curr_template_count
    end

    def used_template_count
      @options[:used_template_count]
    end
  end
end
