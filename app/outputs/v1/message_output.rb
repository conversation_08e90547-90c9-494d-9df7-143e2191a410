# frozen_string_literal: true

module V1
  class MessageOutput < ApiOutput
    def format
      {
        id: @object.id,
        chat_id: @object.chat_id,
        content: @object.content,
        sender: @object.sender,
        created_at: @object.created_at,
        image: url_output(@object.image_url, 'image'),
        file: url_output(@object.file_url, 'file'),
        annotations: web_search_results_output
      }
    end

    private

    def web_search_results_output
      @object.web_search_results.active.map do |result|
        {
          id: result.id,
          url: result.url,
          title: result.title,
          content: result.content,
          start_index: result.start_index,
          end_index: result.end_index,
          image: result.image,
          site_name: result.site_name,
          created_at: result.created_at
        }
      end
    end

    def url_output(urls, type)
      return unless urls

      urls_arr = urls.split(' ')

      if urls_arr.size == 1
        url = urls_arr.first
        uri = URI.parse(url)
        filename = File.basename(uri.path)

        filename = remove_hex_identifier(filename) if type == 'file'

        {
          filename: filename,
          url: url
        }
      else
        new_filenames = ''
        new_urls = ''

        urls_arr.each do |url|
          uri = URI.parse(url)
          filename = File.basename(uri.path)

          filename = remove_hex_identifier(filename) if type == 'file'

          new_filenames += ' ' + filename
          new_urls += ' ' + url
        end

        {
          filename: new_filenames,
          url: new_urls
        }
      end
    end

    def remove_hex_identifier(filename)
      arr_filename = filename.split('.')

      if arr_filename.size > 1
        no_ext_filename = arr_filename[..(arr_filename.size - 2)].join('.')
        ext = arr_filename.last

        arr_no_ext_filename = no_ext_filename.split('-')
        if arr_no_ext_filename.size > 1
          no_hex_filename = arr_no_ext_filename[..(arr_no_ext_filename.size - 2)].join('-')

          filename = [no_hex_filename, ext].join('.')
        end
      end

      filename
    end
  end
end
