# frozen_string_literal: true

module V1
  class AuthOutput < ApiOutput
    def format
      {
        auth_token: @object,
        authenticated: current_user.persisted?,
        user: user_output
      }
    end

    def user_output
      return unless current_user.persisted?

      V1::UserOutput.new(current_user).format
    end

    private

    def current_user
      @options[:current_user]
    end
  end
end
