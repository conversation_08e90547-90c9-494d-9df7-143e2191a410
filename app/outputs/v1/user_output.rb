# frozen_string_literal: true

module V1
  class UserOutput < ApiOutput
    def nano_format
      {
        id: @object.id,
        name: @object.display_name,
        display_name: @object.display_name,
        photo_url: @object.photo_url,
        email: @object.email
      }
    end

    def format
      {
        id: @object.id,
        name: @object.display_name,
        display_name: @object.display_name,
        photo_url: @object.photo_url,
        email: @object.email,
        organization: {
          id: organization.id,
          name: organization.name,
          organization_plan_threshold: {
            purchased_credits: organization_plan_threshold.purchased_credits.to_f,
            remaining_monthly_credits: organization_plan_threshold.remaining_monthly_credits.to_f,
            monthly_credits_refresh: organization_plan_threshold.monthly_credits_refresh,
            refresh_date: organization_plan_threshold.refresh_date,
            max_members: organization_plan_threshold.max_members
          },
          subscription: organization_subscription
        },
        workspace_id: workspace_output,
        role: @object.membership.membership_role,
        organization_team: organization_team_output
      }
    end

    def organization
      @object.membership.organization
    end

    def organization_plan_threshold
      organization.organizations_plans_thresholds.first
    end

    def organization_subscription
      org_subscription = organization.organizations_subscriptions.first

      return free_subscription_output if org_subscription.nil?

      subscription = org_subscription.subscription

      plan = Plan.find(subscription.variant_id)
      product = plan.product

      {
        status: subscription.status,
        renews_at: subscription.renews_at,
        product: {
          name: product.name,
          description: product.description,
          monthly_tokens: product.monthly_tokens
        }
      }
    end

    def free_subscription_output
      {
        status: 'Free',
        renews_at: '',
        product: {
          name: 'Free',
          description: 'A free plan to feel experience in Collabway',
          monthly_tokens: 5000
        }
      }
    end

    def workspace_output
      @object.membership&.workspaces_memberships&.first&.workspace_id
    end

    def organization_team_output
      membership = @object.membership

      return unless membership.present?

      org_team_ids = membership.organization_team_ids
      org_teams = OrganizationTeam.where(id: org_team_ids)

      return unless org_teams.present?

      org_teams.map do |org_team|
        {
          id: org_team.id,
          name: org_team.name
        }
      end
    end
  end
end
