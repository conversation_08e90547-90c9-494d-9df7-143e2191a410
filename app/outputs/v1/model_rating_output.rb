# frozen_string_literal: true

module V1
  class ModelRatingOutput < ApiOutput
    def format
      {
        id: @object.id,
        rating: @object.rating,
        comment: @object.comment,
        feedback: @object.feedback,
        commented_at: @object.created_at,
        model_template: model_template_output,
        user: user_output,
        number_of_used_times: number_of_used_times_output
      }
    end

    def model_template_output
      {
        id: @object.model_template&.id,
        name: @object.model_template&.name
      }
    end

    def user_output
      return if users.blank?

      current_rating_user = users.find { |u| u.id.to_s == @object.user_id.to_s }

      return if current_rating_user.nil?

      role = current_rating_user.memberships&.first&.membership_role

      {
        id: current_rating_user.id,
        name: current_rating_user.display_name,
        role: role
      }
    end

    def number_of_used_times_output
      return 0 unless number_of_used_times

      current_template_used_times = number_of_used_times[@object.user_id]

      return 0 if current_template_used_times.nil?
      
      current_template_used_times
    end

    def number_of_used_times
      @options[:number_of_used_times]
    end

    def users
      @options[:users]
    end
  end
end
