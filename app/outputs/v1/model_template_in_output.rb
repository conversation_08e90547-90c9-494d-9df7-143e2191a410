# frozen_string_literal: true

module V1
  class ModelTemplateInOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description,
        order: @object.order,
        model_template_id: @object.model_template_id,
        input_reference: url_output(@object.input_reference_url)
      }
    end

    def url_output(url)
      return unless url

      uri = URI.parse(url)
      filename = File.basename(uri.path)
      {
        filename: filename,
        url: url
      }
    end
  end
end
