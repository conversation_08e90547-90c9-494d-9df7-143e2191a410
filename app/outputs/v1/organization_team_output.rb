# frozen_string_literal: true

module V1
  class OrganizationTeamOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        organization_id: @object.organization_id,
        member_count: member_count_output
      }
    end

    def member_count_output
      return if memberships.blank?

      memberships.select { |m| m.organization_team_ids.include?(@object.id) }.size
    end
    
    def memberships
      @options[:memberships]
    end
  end
end
