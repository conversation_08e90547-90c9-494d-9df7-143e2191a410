require 'rails_helper'

RSpec.describe ::V1::ModelRatingsController, type: :request do
  let!(:test_user) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let!(:test_user2) do
    User.create!(
      display_name: 'Test2',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let!(:organization) do
    Organization.create!(name: 'Test')
  end
  let!(:workspace) do
    Workspace.create!(
      organization_id: organization.id,
      name: 'Test'
    )
  end
  let!(:membership_test_user) do
    Membership.create!(
      user_id: test_user.id,
      organization_id: organization.id
    )
  end
  let!(:workspace_membership) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user.id,
      workspace_id: workspace.id
    )
  end
  let!(:membership_test_user2) do
    Membership.create!(
      user_id: test_user2.id,
      organization_id: organization.id
    )
  end
  let!(:workspace_membership2) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user2.id,
      workspace_id: workspace.id
    )
  end

  let!(:test_user_org2) do
    User.create!(
      display_name: 'Test Org2',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let!(:organization2) do
    Organization.create!(name: 'Test2')
  end
  let!(:workspace2) do
    Workspace.create!(
      organization_id: organization2.id,
      name: 'Test2'
    )
  end
  let!(:membership_test_user_org2) do
    Membership.create!(
      user_id: test_user_org2.id,
      organization_id: organization2.id
    )
  end
  let!(:workspace_membership_org2) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user_org2.id,
      workspace_id: workspace2.id
    )
  end

  let!(:model) do
    Model.create!(
      name: 'ChatGPT gpt-4o',
      max_tokens: 100,
      temperature: 1.0,
      model: 'gpt-4o',
      instruction: 'Chat GPT',
      openai_assistant_id: 'asst_abc098'
    )
  end

  let!(:model_template) do
    ModelTemplate.create!(
      name: 'Test Template',
      description: 'Test Desc',
      max_tokens: 100,
      temperature: 1.0,
      model: 'gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization.id,
      user: test_user
    )
  end
  let!(:model_template2) do
    ModelTemplate.create!(
      name: 'Test Template2',
      description: 'Test Desc2',
      max_tokens: 100,
      temperature: 1.0,
      model: 'gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization2.id,
      user: test_user_org2
    )
  end

  def index_model_ratings(params, user)
    user_token = JsonWebToken.encode({ user_id: user.id })

    headers = {
      "CONTENT_TYPE" => "application/json",
      "Authorization" => "bearer #{user_token}"
    }

    get "/v1/model_ratings", headers: headers, params: params
  end

  # need to comment before_create line in user model first
  describe "GET list model ratings" do
    before do
      ModelRating.create!(
        rating: 5,
        user_id: test_user.id,
        model_template_id:  model_template.id,
        comment: nil
      )

      ModelRating.create!(
        rating: 4,
        user_id: test_user2.id,
        model_template_id:  model_template.id,
        comment: 'comment 2'
      )

      ModelRating.create!(
        rating: 3,
        user_id: test_user_org2.id,
        model_template_id:  model_template2.id,
        comment: 'comment 3'
      )
    end

    it 'return list model ratings from same organization' do
      expect(ModelRating.all.count).to eq 3
      
      index_model_ratings({}, test_user)
      expect(response.status).to eq 200
        
      response_data = JSON.parse(response.body)['data']
      expect(response_data.size).to eq 2
    end

    it "return filtered list from same organization with 'model_template_id' params" do
      index_model_ratings({model_template_id: -1}, test_user)
      expect(response.status).to eq 200
        
      response_data = JSON.parse(response.body)['data']
      expect(response_data.size).to eq 0
    end

    it "return filtered list from same organization with 'user_id' params" do
      index_model_ratings({user_id: test_user.id}, test_user)
      expect(response.status).to eq 200
        
      response_data = JSON.parse(response.body)['data']
      expect(response_data.size).to eq 1

      expect(response_data.first['user']['id']).to eq test_user.id
    end

    it "return filtered list from same organization with 'comment' params" do
      index_model_ratings({comment: true}, test_user)
      expect(response.status).to eq 200
        
      response_data = JSON.parse(response.body)['data']
      expect(response_data.size).to eq 1

      expect(response_data.first['user']['id']).to eq test_user2.id
    end
  end
end