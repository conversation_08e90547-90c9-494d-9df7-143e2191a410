require 'rails_helper'

RSpec.describe ::V1::ModelTemplateVariablesController, type: :request do
  let(:test_user) do
    User.create(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization) do
    Organization.create(name: 'Test')
  end
  let(:membership_test_user) do
    Membership.create(
      user_id: test_user.id,
      organization_id: organization.id
    )
  end

  let(:organization2) do
    Organization.create(name: 'Test2')
  end
  let(:membership2_test_user) do
    Membership.create(
      user_id: test_user.id,
      organization_id: organization2.id
    )
  end

  let(:model_template) do
    ModelTemplate.create(
      name: 'Test Template',
      description: 'Test Desc',
      max_tokens: 100,
      temperature: 1.0,
      model: 'gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization.id
    )
  end

  let(:model_template_variable) do
    ModelTemplateVariable.create(
      model_template_id: model_template.id,
      name: 'To<PERSON>',
      description: 'Desc'
    )
  end

  def delete_model_template_variables(id, user)
    user_token = JsonWebToken.encode({ user_id: user.id })

    headers = {
      "Authorization" => "bearer #{user_token}"
    }

    delete "/v1/model_template_variables/#{id}", headers: headers
  end

  describe "DELETE model template variable" do
    before do
      # init data
      organization
      test_user
      membership_test_user
      model_template
      model_template_variable
    end

    context "when not from the same organization" do
      before do
        organization2
        model_template.update(organization_id: organization2.id)
      end

      it "return unauthorized" do
        delete_model_template_variables(model_template_variable.id, test_user)

        expect(response.status).to eq 403
      end
    end
            
    context "when does not have related file, model and assistant" do
      it "discard model template variable properly" do
        delete_model_template_variables(model_template_variable.id, test_user)

        expect(response.status).to eq 200

        model_template_variable.reload
        expect(model_template_variable.discarded_at.nil?).to be_falsey
      end
    end

    context "when file present" do
      let!(:openai_file) do
        OpenaiFile.create(
          object_id: model_template_variable.id,
          object_class: model_template_variable.class.name,
          object_class_column: 'variable_reference_url',
          openai_file_id: 'file_xxyyzz1234'
        )
      end

      context "but does not exist in openai" do
        before do 
          allow_any_instance_of(OpenaiService).to receive(:delete_file)
                                              .with(openai_file.openai_file_id)
                                              .and_raise(Faraday::ResourceNotFound)
        end

        it "discard model template variable & file properly" do
          delete_model_template_variables(model_template_variable.id, test_user)
  
          expect(response.status).to eq 200
  
          model_template_variable.reload
          openai_file.reload
          expect(model_template_variable.discarded_at.nil?).to be_falsey
          expect(openai_file.discarded_at.nil?).to be_falsey
        end
      end

      context "and exist in openai" do
        before do 
          allow_any_instance_of(OpenaiService).to receive(:delete_file)
                                              .with(openai_file.openai_file_id)
                                              .and_return({"id" => openai_file.openai_file_id})
        end

        it "discard model template variable & file properly" do
          delete_model_template_variables(model_template_variable.id, test_user)
  
          expect(response.status).to eq 200
  
          model_template_variable.reload
          openai_file.reload
          expect(model_template_variable.discarded_at.nil?).to be_falsey
          expect(openai_file.discarded_at.nil?).to be_falsey
        end
      end
    end
  end
end