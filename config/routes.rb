require_relative 'routes_helpers'
require 'sidekiq/web'
require 'sidekiq/cron/web'

Rails.application.routes.draw do
  extend RoutesHelpers

  namespace :v1 do
    # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html
    # post 'stream', to: 'openai#stream_response'
    post 'stream_v2', to: 'openai#stream_response_v2'
    # post 'chat_stream', to: 'openai#chat_stream'
    post 'chat_stream_v2', to: 'openai#chat_stream_v2'

    post 'chats', to: 'chats#create'
    put 'chats/:id', to: 'chats#update'
    delete 'chats/:id', to: 'chats#delete'
    post 'workspaces/:workspace_id/chats', to: 'chats#create'
    put 'workspaces/:workspace_id/chats/:id', to: 'chats#update'
    get 'workspaces/:workspace_id/chats', to: 'chats#list'
    get 'workspaces/:workspace_id/chats/:chat_id', to: 'chats#show'
    delete 'workspaces/:workspace_id/chats/:chat_id', to: 'chats#delete'

    get 'chats/share/:external_id', to: 'public#show_chat'
    get 'workspaces/:workspace_id', to: 'openai#show_workspace'

    post 'model_template_variables', to: 'model_template_variables#create'
    put 'model_template_variables/:id', to: 'model_template_variables#update'
    get 'model_template_variables', to: 'model_template_variables#list'
    delete 'model_template_variables/:id', to: 'model_template_variables#destroy'

    get 's3/uploads/presign', to: 's3#upload_s3'

    resources :model_templates, only: %i[create update show index destroy] do
      post 'duplicate', to: 'model_templates#duplicate'
      get 'comments', to: 'model_templates#list_comments'
    end

    resources :template_categories, only: %i[create update show index destroy]
    resources :users, only: %i[show update]
    resources :memberships, only: %i[index update destroy]
    resources :model_ratings, only: %i[create update index destroy]
    resources :organizations, only: %i[create update show index] do
      post 'transfer_ownership', to: 'organizations#transfer_ownership'
    end
    resources :organization_teams, only: %i[create update show index destroy]
    resources :model_template_ins, only: %i[create update index destroy]
    resources :store_items, only: %i[create update show index destroy]
    resources :invoices, only: %i[create show index]
    post 'invoices/handle_callback', to: 'invoices#handle_callback'
    post 'user_managements/invite', to: 'user_managements#create_invitation'
    post 'user_managements/accept_invitation', to: 'user_managements#accept_invitation'
    get 'user_managements/invitation_code_details', to: 'user_managements#detail_invitation'
    post 'user_managements/change_password_request', to: 'user_managements#change_password_request'
    post 'user_managements/change_password', to: 'user_managements#change_password'

    post 'login', to: 'auth#authenticate'
    get 'auth', to: 'auth#show'
    get 'users_get_by_email', to: 'users#get_by_email'

    get 'citations/:id', to: 'citations#show'
  end

  mount Sidekiq::Web => '/sidekiq'

  # Defines the root path route ("/")
  #################
  # INSTRUMENTATION ENDPOINTS
  #################

  get 'version' => proc { |_env|
    rack_json(version: CollabwayApi::VERSION, up_since: CollabwayApi::UP_SINCE)
  }
end
