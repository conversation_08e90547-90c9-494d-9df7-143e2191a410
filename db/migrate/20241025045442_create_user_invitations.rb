class CreateUserInvitations < ActiveRecord::Migration[7.0]
  def change
    create_table :user_invitations do |t|
      t.string :email
      t.string :invitation_status
      t.string :invitation_code
      t.datetime :invitation_expiry_date
      t.references :organization
      t.bigint :invited_to_organization_team_id, :index => true
      t.bigint :invited_by_membership_id, :index => true
      t.integer :role
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
