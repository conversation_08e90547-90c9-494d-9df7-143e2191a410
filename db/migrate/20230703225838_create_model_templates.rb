class CreateModelTemplates < ActiveRecord::Migration[7.0]
  def change
    create_table :model_templates do |t|
      t.string :name, null: false
      t.string :description, null: false
      t.integer :max_tokens, default: 2000
      t.float :temperature, default: 1
      t.string :model, default: 'gpt-3.5-turbo'
      t.string :instruction, default: 'You are ChatGPT, answer as helpful as possible!'
      t.string :prompt
      t.string :placeholder
      t.string :template_type
      t.integer :chat_id

      t.timestamps
    end
  end
end
