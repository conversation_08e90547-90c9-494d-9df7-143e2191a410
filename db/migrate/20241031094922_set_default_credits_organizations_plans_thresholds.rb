class SetDefaultCreditsOrganizationsPlansThresholds < ActiveRecord::Migration[7.0]
  def change
    OrganizationsPlansThreshold.where(purchased_credits: nil).update_all(purchased_credits: 0)
    change_column :organizations_plans_thresholds, :purchased_credits, :integer, :default => 0, :null => false

    OrganizationsPlansThreshold.where(remaining_monthly_credits: nil).update_all(remaining_monthly_credits: 0)
    change_column :organizations_plans_thresholds, :remaining_monthly_credits, :integer, :default => 0, :null => false
  end
end
