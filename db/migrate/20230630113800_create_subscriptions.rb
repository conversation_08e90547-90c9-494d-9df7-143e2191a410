class CreateSubscriptions < ActiveRecord::Migration[7.0]
  def change
    unless table_exists?(:subscriptions)
      create_table :subscriptions do |t|
        t.integer :variant_id
        t.string :status
        t.boolean :cancel_at_period_end
        t.datetime :created_at
        t.datetime :trial_starts_at
        t.datetime :trial_ends_at

        t.integer :billing_anchor
        t.datetime :ends_at
        t.datetime :renews_at

        t.string :update_payment_method_url
      end
    end
  end
end
