class CreateTokenRequests < ActiveRecord::Migration[7.0]
  def change
    create_table :token_requests do |t|
      t.string :email
      t.string :user_id, :index => true
      t.string :purpose
      t.string :request_status
      t.string :request_code
      t.datetime :request_expiry_date, :index => true
      t.datetime :requested_at, :index => true
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
