class AddDiscardedAtToOrganizationalRelatedTables < ActiveRecord::Migration[7.0]
  def change
    add_column :memberships, :discarded_at, :datetime
    add_index :memberships, :discarded_at

    add_column :organizations, :discarded_at, :datetime
    add_index :organizations, :discarded_at

    add_column :organizations_plans_thresholds, :discarded_at, :datetime
    add_index :organizations_plans_thresholds, :discarded_at

    add_column :users, :discarded_at, :datetime
    add_index :users, :discarded_at

    add_column :workspaces, :discarded_at, :datetime
    add_index :workspaces, :discarded_at

    add_column :workspaces_memberships, :discarded_at, :datetime
    add_index :workspaces_memberships, :discarded_at
  end
end
