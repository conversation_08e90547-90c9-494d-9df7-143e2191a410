class CreateStoreItems < ActiveRecord::Migration[7.0]
  def change
    create_table :store_items do |t|
      t.string :name
      t.string :store_item_type, :default => 'one_time_purchase_token', :null => false
      t.integer :token_amounts
      t.integer :price, :default => 0, :null => false
      t.integer :price_decimal, :default => 0, :null => false
      t.string :currency, :default => 'USD', :null => false
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
