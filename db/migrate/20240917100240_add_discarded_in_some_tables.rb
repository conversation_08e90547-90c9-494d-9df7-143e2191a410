class AddDiscardedInSomeTables < ActiveRecord::Migration[7.0]
  def change
    unless column_exists?(:chats, :discarded_at)
      add_column :chats, :discarded_at, :datetime
      add_index :chats, :discarded_at
    end

    add_column :messages, :discarded_at, :datetime
    add_index :messages, :discarded_at

    add_column :model_ratings, :discarded_at, :datetime
    add_index :model_ratings, :discarded_at

    add_column :model_template_variables, :discarded_at, :datetime
    add_index :model_template_variables, :discarded_at

    add_column :model_templates, :discarded_at, :datetime
    add_index :model_templates, :discarded_at

    add_column :models, :discarded_at, :datetime
    add_index :models, :discarded_at

    add_column :openai_chats, :discarded_at, :datetime
    add_index :openai_chats, :discarded_at

    add_column :openai_files, :discarded_at, :datetime
    add_index :openai_files, :discarded_at

    add_column :template_categories, :discarded_at, :datetime
    add_index :template_categories, :discarded_at
  end
end
