class CreateWebSearchResults < ActiveRecord::Migration[7.0]
  def change
    create_table 'web_search_results' do |t|
      t.references :message, null: false, foreign_key: true
      t.string :url, null: false
      t.string :title
      t.text :content
      t.integer :start_index
      t.integer :end_index
      t.datetime :created_at, null: false
      t.datetime :updated_at, null: false
      t.datetime :discarded_at
    end

    add_index :web_search_results, :discarded_at
    add_index :web_search_results, :url
  end
end
