class ChangeOrganizationTeamIdToArrayInMemberships < ActiveRecord::Migration[7.0]
  def up
    rename_column :memberships, :organization_team_id, :organization_team_ids
    change_column :memberships, :organization_team_ids, :integer, :array => true, using: 'array[organization_team_ids]::INTEGER[]', :default => []
  end

  def down
    change_column :memberships, :organization_team_ids, :integer, using: 'organization_team_ids[1]::INTEGER', :default => nil
    rename_column :memberships, :organization_team_ids, :organization_team_id
  end
end
