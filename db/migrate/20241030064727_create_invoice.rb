class CreateInvoice < ActiveRecord::Migration[7.0]
  def change
    create_table :invoices do |t|
      t.string :name
      t.string :description
      t.string :status
      t.integer :checkout_price
      t.integer :checkout_price_decimal
      t.integer :checkout_token_amounts
      t.string :checkout_currency
      t.string :payment_intent_id
      t.string :payment_confirmation_request_id
      t.string :payment_merchant_order_id
      t.string :payment_request_id
      t.string :payment_client_secret
      t.string :payment_status
      t.references :store_item
      t.references :organization
      t.string :user_id, :index => true
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
