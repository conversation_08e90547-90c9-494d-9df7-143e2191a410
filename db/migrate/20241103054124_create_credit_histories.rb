class CreateCreditHistories < ActiveRecord::Migration[7.0]
  def change
    create_table :credit_histories do |t|
      t.references :organization
      t.string :action
      t.integer :monthly_credits
      t.integer :purchased_credits
      t.references :invoice
      t.datetime :action_at, :index => true
      t.string :user_id, :index => true
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
