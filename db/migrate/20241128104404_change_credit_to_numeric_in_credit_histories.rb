class ChangeCreditToNumericInCreditHistories < ActiveRecord::Migration[7.0]
  def up
    change_column :credit_histories, :monthly_credits, :decimal, precision: 30, scale: 9
    change_column :credit_histories, :purchased_credits, :decimal, precision: 30, scale: 9
  end

  def down 
    change_column :credit_histories, :monthly_credits, :integer
    change_column :credit_histories, :purchased_credits, :integer
  end
end
