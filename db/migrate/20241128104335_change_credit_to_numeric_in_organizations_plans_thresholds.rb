class ChangeCreditToNumericInOrganizationsPlansThresholds < ActiveRecord::Migration[7.0]
  def up
    change_column :organizations_plans_thresholds, :purchased_credits, :decimal, precision: 30, scale: 9
    change_column :organizations_plans_thresholds, :remaining_monthly_credits, :decimal, precision: 30, scale: 9
  end

  def def down 
    change_column :organizations_plans_thresholds, :purchased_credits, :integer
    change_column :organizations_plans_thresholds, :remaining_monthly_credits, :integer
  end
end
