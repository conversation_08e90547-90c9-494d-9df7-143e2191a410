class CreateModels < ActiveRecord::Migration[7.0]
  def change
    unless table_exists?(:models)
      create_table :models do |t|
        t.integer :organization_id
        t.string :name
        t.string :model
        t.string :instruction
        t.float :temperature
        t.float :top_p
        t.float :frequency_penalty
        t.float :presence_penalty
        t.integer :max_tokens

        t.timestamps
      end
    end
  end
end
