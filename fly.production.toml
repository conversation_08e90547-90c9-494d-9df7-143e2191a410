# fly.toml app configuration file generated for collabway-api on 2023-06-30T09:17:12+07:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "collabway-api"
primary_region = "sin"
console_command = "/rails/bin/rails console"

[processes]
  app = "./bin/rails server"
  worker = "bundle exec sidekiq"

[http_service]
  processes = ["app"]
  internal_port = 3000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 0

[[statics]]
  guest_path = "/rails/public"
  url_prefix = "/"

[mounts]
  source="brandrev1"
  destination="/data"

[[vm]]
  size = "shared-cpu-1x"
  memory = "2gb"
  
